#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/base_state.h
array
-

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/data_buffer.hpp
deque
-
memory
-
mutex
-
shared_mutex
-

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/humanoid.cpp
humanoid.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/humanoid.hpp

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/humanoid.hpp
iostream
-
stdint.h
-
string
-
unitree/robot/channel/channel_publisher.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/unitree/robot/channel/channel_publisher.hpp
unitree/robot/channel/channel_subscriber.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/unitree/robot/channel/channel_subscriber.hpp
unitree/common/thread/thread.hpp
-
unitree/idl/go2/LowCmd_.hpp
-
unitree/idl/go2/LowState_.hpp
-
base_state.h
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/base_state.h
data_buffer.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/data_buffer.hpp
motors.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/motors.hpp

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/humanoid/low_level/motors.hpp
array
-
stdint.h
-
unitree/idl/go2/LowCmd_.hpp
-

