#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/low_level/low_level.cpp
iostream
-
stdio.h
-
stdint.h
-
math.h
-
unitree/robot/channel/channel_publisher.hpp
-
unitree/robot/channel/channel_subscriber.hpp
-
unitree/idl/go2/LowState_.hpp
-
unitree/idl/go2/LowCmd_.hpp
-
unitree/common/time/time_tool.hpp
-
unitree/common/thread/thread.hpp
-

