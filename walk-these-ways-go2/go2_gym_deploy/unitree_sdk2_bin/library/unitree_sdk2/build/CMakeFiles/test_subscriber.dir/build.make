# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build

# Include any dependencies generated for this target.
include CMakeFiles/test_subscriber.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/test_subscriber.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_subscriber.dir/flags.make

CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.o: CMakeFiles/test_subscriber.dir/flags.make
CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.o: ../example/helloworld/subscriber.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.o -c /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/subscriber.cpp

CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/subscriber.cpp > CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.i

CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/subscriber.cpp -o CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.s

CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.o: CMakeFiles/test_subscriber.dir/flags.make
CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.o: ../example/helloworld/HelloWorldData.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.o -c /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/HelloWorldData.cpp

CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/HelloWorldData.cpp > CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.i

CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/HelloWorldData.cpp -o CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.s

# Object files for target test_subscriber
test_subscriber_OBJECTS = \
"CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.o" \
"CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.o"

# External object files for target test_subscriber
test_subscriber_EXTERNAL_OBJECTS =

test_subscriber: CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.o
test_subscriber: CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.o
test_subscriber: CMakeFiles/test_subscriber.dir/build.make
test_subscriber: CMakeFiles/test_subscriber.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable test_subscriber"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_subscriber.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_subscriber.dir/build: test_subscriber

.PHONY : CMakeFiles/test_subscriber.dir/build

CMakeFiles/test_subscriber.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_subscriber.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_subscriber.dir/clean

CMakeFiles/test_subscriber.dir/depend:
	cd /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2 /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2 /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles/test_subscriber.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_subscriber.dir/depend

