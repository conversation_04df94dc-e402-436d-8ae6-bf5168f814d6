# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build

# Include any dependencies generated for this target.
include CMakeFiles/stand_example_go2.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/stand_example_go2.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/stand_example_go2.dir/flags.make

CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.o: CMakeFiles/stand_example_go2.dir/flags.make
CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.o: ../example/low_level/stand_example_go2.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.o -c /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/low_level/stand_example_go2.cpp

CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/low_level/stand_example_go2.cpp > CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.i

CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/low_level/stand_example_go2.cpp -o CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.s

# Object files for target stand_example_go2
stand_example_go2_OBJECTS = \
"CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.o"

# External object files for target stand_example_go2
stand_example_go2_EXTERNAL_OBJECTS =

stand_example_go2: CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.o
stand_example_go2: CMakeFiles/stand_example_go2.dir/build.make
stand_example_go2: CMakeFiles/stand_example_go2.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable stand_example_go2"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/stand_example_go2.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/stand_example_go2.dir/build: stand_example_go2

.PHONY : CMakeFiles/stand_example_go2.dir/build

CMakeFiles/stand_example_go2.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/stand_example_go2.dir/cmake_clean.cmake
.PHONY : CMakeFiles/stand_example_go2.dir/clean

CMakeFiles/stand_example_go2.dir/depend:
	cd /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2 /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2 /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles/stand_example_go2.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/stand_example_go2.dir/depend

