#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/HelloWorldData.cpp
HelloWorldData.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/HelloWorldData.hpp

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/HelloWorldData.hpp
cstdint
-
string
-
dds/topic/TopicTraits.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/dds/topic/TopicTraits.hpp
org/eclipse/cyclonedds/topic/datatopic.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/org/eclipse/cyclonedds/topic/datatopic.hpp

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/publisher.cpp
unitree/robot/channel/channel_publisher.hpp
-
unitree/common/time/time_tool.hpp
-
HelloWorldData.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/helloworld/HelloWorldData.hpp

/usr/local/include/ddscxx/dds/core/detail/export.hpp

/usr/local/include/ddscxx/dds/core/detail/macros.hpp
iostream
-
string.h
-
dds/core/detail/export.hpp
/usr/local/include/ddscxx/dds/core/detail/dds/core/detail/export.hpp
dds/core/detail/maplog.hpp
-
cstring
-

/usr/local/include/ddscxx/dds/core/macros.hpp
dds/core/detail/macros.hpp
-

/usr/local/include/ddscxx/dds/features.hpp

/usr/local/include/ddscxx/dds/topic/TopicTraits.hpp
string
-

/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/Missing.hpp

/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/basic_cdr_ser.hpp
cdr_stream.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/cdr_stream.hpp

/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/cdr_enums.hpp

/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/cdr_stream.hpp
dds/ddsrt/endian.h
/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/dds/ddsrt/endian.h
org/eclipse/cyclonedds/core/type_helpers.hpp
-
org/eclipse/cyclonedds/core/cdr/entity_properties.hpp
-
stdint.h
-
string
-
stdexcept
-
stack
-
cassert
-
dds/core/macros.hpp
-

/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/entity_properties.hpp
dds/core/macros.hpp
-
cstdint
-
list
-
vector
-
map
-
atomic
-
mutex
-
boost/type_traits.hpp
-
type_traits
-
cdr_enums.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/cdr_enums.hpp

/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/extended_cdr_v1_ser.hpp
cdr_stream.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/cdr_stream.hpp

/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/extended_cdr_v2_ser.hpp
cdr_stream.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/cdr_stream.hpp

/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/cdr/fragchain.hpp
cstddef
-
dds/core/macros.hpp
-

/usr/local/include/ddscxx/org/eclipse/cyclonedds/core/type_helpers.hpp
type_traits
-
org/eclipse/cyclonedds/core/Missing.hpp
-

/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/TopicTraits.hpp
dds/ddsrt/heap.h
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/dds/ddsrt/heap.h
dds/ddsi/ddsi_serdata.h
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/dds/ddsi/ddsi_serdata.h
org/eclipse/cyclonedds/core/cdr/cdr_enums.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/org/eclipse/cyclonedds/core/cdr/cdr_enums.hpp
dds/features.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/dds/features.hpp

/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/datatopic.hpp
memory
-
string
-
cstring
-
vector
-
atomic
-
dds/ddsrt/md5.h
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/dds/ddsrt/md5.h
org/eclipse/cyclonedds/core/cdr/basic_cdr_ser.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/org/eclipse/cyclonedds/core/cdr/basic_cdr_ser.hpp
org/eclipse/cyclonedds/core/cdr/extended_cdr_v1_ser.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/org/eclipse/cyclonedds/core/cdr/extended_cdr_v1_ser.hpp
org/eclipse/cyclonedds/core/cdr/extended_cdr_v2_ser.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/org/eclipse/cyclonedds/core/cdr/extended_cdr_v2_ser.hpp
org/eclipse/cyclonedds/core/cdr/fragchain.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/org/eclipse/cyclonedds/core/cdr/fragchain.hpp
org/eclipse/cyclonedds/topic/TopicTraits.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/org/eclipse/cyclonedds/topic/TopicTraits.hpp
org/eclipse/cyclonedds/topic/hash.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/org/eclipse/cyclonedds/topic/hash.hpp
dds/ddsi/ddsi_shm_transport.h
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/dds/ddsi/ddsi_shm_transport.h

/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/hash.hpp
dds/core/macros.hpp
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/dds/core/macros.hpp
dds/ddsi/ddsi_keyhash.h
/usr/local/include/ddscxx/org/eclipse/cyclonedds/topic/dds/ddsi/ddsi_keyhash.h
vector
-

