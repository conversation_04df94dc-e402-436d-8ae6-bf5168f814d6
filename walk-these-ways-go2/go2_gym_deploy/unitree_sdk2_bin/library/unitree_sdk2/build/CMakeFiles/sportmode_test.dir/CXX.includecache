#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/high_level/sportmode_test.cpp
cmath
-
unitree/robot/go2/sport/sport_client.hpp
-
unitree/robot/channel/channel_subscriber.hpp
-
unitree/idl/go2/SportModeState_.hpp
-

