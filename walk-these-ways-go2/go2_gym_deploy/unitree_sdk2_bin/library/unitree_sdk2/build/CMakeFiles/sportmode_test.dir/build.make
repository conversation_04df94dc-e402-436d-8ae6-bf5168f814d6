# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build

# Include any dependencies generated for this target.
include CMakeFiles/sportmode_test.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/sportmode_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/sportmode_test.dir/flags.make

CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.o: CMakeFiles/sportmode_test.dir/flags.make
CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.o: ../example/high_level/sportmode_test.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.o -c /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/high_level/sportmode_test.cpp

CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/high_level/sportmode_test.cpp > CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.i

CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/high_level/sportmode_test.cpp -o CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.s

# Object files for target sportmode_test
sportmode_test_OBJECTS = \
"CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.o"

# External object files for target sportmode_test
sportmode_test_EXTERNAL_OBJECTS =

sportmode_test: CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.o
sportmode_test: CMakeFiles/sportmode_test.dir/build.make
sportmode_test: CMakeFiles/sportmode_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable sportmode_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/sportmode_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/sportmode_test.dir/build: sportmode_test

.PHONY : CMakeFiles/sportmode_test.dir/build

CMakeFiles/sportmode_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/sportmode_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/sportmode_test.dir/clean

CMakeFiles/sportmode_test.dir/depend:
	cd /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2 /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2 /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles/sportmode_test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/sportmode_test.dir/depend

