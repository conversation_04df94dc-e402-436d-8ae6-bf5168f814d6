#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/cfg.hpp
unitree/common/json/jsonize.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/unitree/common/json/jsonize.hpp
vector
-
iostream
-

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/comm.h
stdint.h
-
array
-

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/conversion.hpp
array
-
comm.h
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/comm.h
unitree/idl/go2/LowCmd_.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/unitree/idl/go2/LowCmd_.hpp

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/gamepad.hpp
cmath
-

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/main.cpp
iostream
-
sstream
-
fstream
-
string
-
vector
-
filesystem
-
algorithm
-
optional
-
chrono
-
iomanip
-
robot_controller.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/robot_controller.hpp
user_controller.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/user_controller.hpp
robot_interface.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/robot_interface.hpp

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/robot_controller.hpp
math.h
-
iostream
-
stdio.h
-
stdint.h
-
vector
-
array
-
chrono
-
thread
-
filesystem
-
mutex
-
fstream
-
future
-
algorithm
-
unitree/idl/go2/LowState_.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/unitree/idl/go2/LowState_.hpp
unitree/idl/go2/LowCmd_.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/unitree/idl/go2/LowCmd_.hpp
unitree/common/thread/thread.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/unitree/common/thread/thread.hpp
unitree/robot/channel/channel_publisher.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/unitree/robot/channel/channel_publisher.hpp
unitree/robot/channel/channel_subscriber.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/unitree/robot/channel/channel_subscriber.hpp
unitree/common/time/time_tool.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/unitree/common/time/time_tool.hpp
state_machine.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/state_machine.hpp
gamepad.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/gamepad.hpp
robot_interface.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/robot_interface.hpp

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/robot_interface.hpp
array
-
iostream
-
comm.h
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/comm.h
unitree/idl/go2/LowState_.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/unitree/idl/go2/LowState_.hpp
unitree/idl/go2/LowCmd_.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/unitree/idl/go2/LowCmd_.hpp
conversion.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/conversion.hpp

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/state_machine.hpp
algorithm
-

/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/user_controller.hpp
array
-
vector
-
filesystem
-
fstream
-
string
-
robot_interface.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/robot_interface.hpp
gamepad.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/gamepad.hpp
cfg.hpp
/home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/example/state_machine/cfg.hpp

