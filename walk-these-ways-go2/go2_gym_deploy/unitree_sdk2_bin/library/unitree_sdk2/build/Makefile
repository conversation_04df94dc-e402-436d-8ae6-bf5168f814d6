# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/walk-these-ways-go2/go2_gym_deploy/unitree_sdk2_bin/library/unitree_sdk2/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named h1_arm_sdk_dds_example

# Build rule for target.
h1_arm_sdk_dds_example: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 h1_arm_sdk_dds_example
.PHONY : h1_arm_sdk_dds_example

# fast build rule for target.
h1_arm_sdk_dds_example/fast:
	$(MAKE) -f CMakeFiles/h1_arm_sdk_dds_example.dir/build.make CMakeFiles/h1_arm_sdk_dds_example.dir/build
.PHONY : h1_arm_sdk_dds_example/fast

#=============================================================================
# Target rules for targets named h1_low_level_example

# Build rule for target.
h1_low_level_example: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 h1_low_level_example
.PHONY : h1_low_level_example

# fast build rule for target.
h1_low_level_example/fast:
	$(MAKE) -f CMakeFiles/h1_low_level_example.dir/build.make CMakeFiles/h1_low_level_example.dir/build
.PHONY : h1_low_level_example/fast

#=============================================================================
# Target rules for targets named test_publisher

# Build rule for target.
test_publisher: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 test_publisher
.PHONY : test_publisher

# fast build rule for target.
test_publisher/fast:
	$(MAKE) -f CMakeFiles/test_publisher.dir/build.make CMakeFiles/test_publisher.dir/build
.PHONY : test_publisher/fast

#=============================================================================
# Target rules for targets named video_client_example

# Build rule for target.
video_client_example: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 video_client_example
.PHONY : video_client_example

# fast build rule for target.
video_client_example/fast:
	$(MAKE) -f CMakeFiles/video_client_example.dir/build.make CMakeFiles/video_client_example.dir/build
.PHONY : video_client_example/fast

#=============================================================================
# Target rules for targets named test_subscriber

# Build rule for target.
test_subscriber: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 test_subscriber
.PHONY : test_subscriber

# fast build rule for target.
test_subscriber/fast:
	$(MAKE) -f CMakeFiles/test_subscriber.dir/build.make CMakeFiles/test_subscriber.dir/build
.PHONY : test_subscriber/fast

#=============================================================================
# Target rules for targets named h1_loco_example_client

# Build rule for target.
h1_loco_example_client: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 h1_loco_example_client
.PHONY : h1_loco_example_client

# fast build rule for target.
h1_loco_example_client/fast:
	$(MAKE) -f CMakeFiles/h1_loco_example_client.dir/build.make CMakeFiles/h1_loco_example_client.dir/build
.PHONY : h1_loco_example_client/fast

#=============================================================================
# Target rules for targets named test_jsonize

# Build rule for target.
test_jsonize: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 test_jsonize
.PHONY : test_jsonize

# fast build rule for target.
test_jsonize/fast:
	$(MAKE) -f CMakeFiles/test_jsonize.dir/build.make CMakeFiles/test_jsonize.dir/build
.PHONY : test_jsonize/fast

#=============================================================================
# Target rules for targets named vui_client_example

# Build rule for target.
vui_client_example: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 vui_client_example
.PHONY : vui_client_example

# fast build rule for target.
vui_client_example/fast:
	$(MAKE) -f CMakeFiles/vui_client_example.dir/build.make CMakeFiles/vui_client_example.dir/build
.PHONY : vui_client_example/fast

#=============================================================================
# Target rules for targets named stand_example_b2

# Build rule for target.
stand_example_b2: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 stand_example_b2
.PHONY : stand_example_b2

# fast build rule for target.
stand_example_b2/fast:
	$(MAKE) -f CMakeFiles/stand_example_b2.dir/build.make CMakeFiles/stand_example_b2.dir/build
.PHONY : stand_example_b2/fast

#=============================================================================
# Target rules for targets named sport_client_example

# Build rule for target.
sport_client_example: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 sport_client_example
.PHONY : sport_client_example

# fast build rule for target.
sport_client_example/fast:
	$(MAKE) -f CMakeFiles/sport_client_example.dir/build.make CMakeFiles/sport_client_example.dir/build
.PHONY : sport_client_example/fast

#=============================================================================
# Target rules for targets named low_level

# Build rule for target.
low_level: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 low_level
.PHONY : low_level

# fast build rule for target.
low_level/fast:
	$(MAKE) -f CMakeFiles/low_level.dir/build.make CMakeFiles/low_level.dir/build
.PHONY : low_level/fast

#=============================================================================
# Target rules for targets named stand_example_go2

# Build rule for target.
stand_example_go2: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 stand_example_go2
.PHONY : stand_example_go2

# fast build rule for target.
stand_example_go2/fast:
	$(MAKE) -f CMakeFiles/stand_example_go2.dir/build.make CMakeFiles/stand_example_go2.dir/build
.PHONY : stand_example_go2/fast

#=============================================================================
# Target rules for targets named wireless

# Build rule for target.
wireless: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 wireless
.PHONY : wireless

# fast build rule for target.
wireless/fast:
	$(MAKE) -f CMakeFiles/wireless.dir/build.make CMakeFiles/wireless.dir/build
.PHONY : wireless/fast

#=============================================================================
# Target rules for targets named high_follow_sin

# Build rule for target.
high_follow_sin: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 high_follow_sin
.PHONY : high_follow_sin

# fast build rule for target.
high_follow_sin/fast:
	$(MAKE) -f CMakeFiles/high_follow_sin.dir/build.make CMakeFiles/high_follow_sin.dir/build
.PHONY : high_follow_sin/fast

#=============================================================================
# Target rules for targets named advanced_gamepad

# Build rule for target.
advanced_gamepad: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 advanced_gamepad
.PHONY : advanced_gamepad

# fast build rule for target.
advanced_gamepad/fast:
	$(MAKE) -f CMakeFiles/advanced_gamepad.dir/build.make CMakeFiles/advanced_gamepad.dir/build
.PHONY : advanced_gamepad/fast

#=============================================================================
# Target rules for targets named state_machine_example

# Build rule for target.
state_machine_example: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 state_machine_example
.PHONY : state_machine_example

# fast build rule for target.
state_machine_example/fast:
	$(MAKE) -f CMakeFiles/state_machine_example.dir/build.make CMakeFiles/state_machine_example.dir/build
.PHONY : state_machine_example/fast

#=============================================================================
# Target rules for targets named sportmode_test

# Build rule for target.
sportmode_test: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 sportmode_test
.PHONY : sportmode_test

# fast build rule for target.
sportmode_test/fast:
	$(MAKE) -f CMakeFiles/sportmode_test.dir/build.make CMakeFiles/sportmode_test.dir/build
.PHONY : sportmode_test/fast

#=============================================================================
# Target rules for targets named robot_state_client_example

# Build rule for target.
robot_state_client_example: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 robot_state_client_example
.PHONY : robot_state_client_example

# fast build rule for target.
robot_state_client_example/fast:
	$(MAKE) -f CMakeFiles/robot_state_client_example.dir/build.make CMakeFiles/robot_state_client_example.dir/build
.PHONY : robot_state_client_example/fast

example/advanced_gamepad/main.o: example/advanced_gamepad/main.cpp.o

.PHONY : example/advanced_gamepad/main.o

# target to build an object file
example/advanced_gamepad/main.cpp.o:
	$(MAKE) -f CMakeFiles/advanced_gamepad.dir/build.make CMakeFiles/advanced_gamepad.dir/example/advanced_gamepad/main.cpp.o
.PHONY : example/advanced_gamepad/main.cpp.o

example/advanced_gamepad/main.i: example/advanced_gamepad/main.cpp.i

.PHONY : example/advanced_gamepad/main.i

# target to preprocess a source file
example/advanced_gamepad/main.cpp.i:
	$(MAKE) -f CMakeFiles/advanced_gamepad.dir/build.make CMakeFiles/advanced_gamepad.dir/example/advanced_gamepad/main.cpp.i
.PHONY : example/advanced_gamepad/main.cpp.i

example/advanced_gamepad/main.s: example/advanced_gamepad/main.cpp.s

.PHONY : example/advanced_gamepad/main.s

# target to generate assembly for a file
example/advanced_gamepad/main.cpp.s:
	$(MAKE) -f CMakeFiles/advanced_gamepad.dir/build.make CMakeFiles/advanced_gamepad.dir/example/advanced_gamepad/main.cpp.s
.PHONY : example/advanced_gamepad/main.cpp.s

example/client/robot_state_client_example.o: example/client/robot_state_client_example.cpp.o

.PHONY : example/client/robot_state_client_example.o

# target to build an object file
example/client/robot_state_client_example.cpp.o:
	$(MAKE) -f CMakeFiles/robot_state_client_example.dir/build.make CMakeFiles/robot_state_client_example.dir/example/client/robot_state_client_example.cpp.o
.PHONY : example/client/robot_state_client_example.cpp.o

example/client/robot_state_client_example.i: example/client/robot_state_client_example.cpp.i

.PHONY : example/client/robot_state_client_example.i

# target to preprocess a source file
example/client/robot_state_client_example.cpp.i:
	$(MAKE) -f CMakeFiles/robot_state_client_example.dir/build.make CMakeFiles/robot_state_client_example.dir/example/client/robot_state_client_example.cpp.i
.PHONY : example/client/robot_state_client_example.cpp.i

example/client/robot_state_client_example.s: example/client/robot_state_client_example.cpp.s

.PHONY : example/client/robot_state_client_example.s

# target to generate assembly for a file
example/client/robot_state_client_example.cpp.s:
	$(MAKE) -f CMakeFiles/robot_state_client_example.dir/build.make CMakeFiles/robot_state_client_example.dir/example/client/robot_state_client_example.cpp.s
.PHONY : example/client/robot_state_client_example.cpp.s

example/client/sport_client_example.o: example/client/sport_client_example.cpp.o

.PHONY : example/client/sport_client_example.o

# target to build an object file
example/client/sport_client_example.cpp.o:
	$(MAKE) -f CMakeFiles/sport_client_example.dir/build.make CMakeFiles/sport_client_example.dir/example/client/sport_client_example.cpp.o
.PHONY : example/client/sport_client_example.cpp.o

example/client/sport_client_example.i: example/client/sport_client_example.cpp.i

.PHONY : example/client/sport_client_example.i

# target to preprocess a source file
example/client/sport_client_example.cpp.i:
	$(MAKE) -f CMakeFiles/sport_client_example.dir/build.make CMakeFiles/sport_client_example.dir/example/client/sport_client_example.cpp.i
.PHONY : example/client/sport_client_example.cpp.i

example/client/sport_client_example.s: example/client/sport_client_example.cpp.s

.PHONY : example/client/sport_client_example.s

# target to generate assembly for a file
example/client/sport_client_example.cpp.s:
	$(MAKE) -f CMakeFiles/sport_client_example.dir/build.make CMakeFiles/sport_client_example.dir/example/client/sport_client_example.cpp.s
.PHONY : example/client/sport_client_example.cpp.s

example/client/video_client_example.o: example/client/video_client_example.cpp.o

.PHONY : example/client/video_client_example.o

# target to build an object file
example/client/video_client_example.cpp.o:
	$(MAKE) -f CMakeFiles/video_client_example.dir/build.make CMakeFiles/video_client_example.dir/example/client/video_client_example.cpp.o
.PHONY : example/client/video_client_example.cpp.o

example/client/video_client_example.i: example/client/video_client_example.cpp.i

.PHONY : example/client/video_client_example.i

# target to preprocess a source file
example/client/video_client_example.cpp.i:
	$(MAKE) -f CMakeFiles/video_client_example.dir/build.make CMakeFiles/video_client_example.dir/example/client/video_client_example.cpp.i
.PHONY : example/client/video_client_example.cpp.i

example/client/video_client_example.s: example/client/video_client_example.cpp.s

.PHONY : example/client/video_client_example.s

# target to generate assembly for a file
example/client/video_client_example.cpp.s:
	$(MAKE) -f CMakeFiles/video_client_example.dir/build.make CMakeFiles/video_client_example.dir/example/client/video_client_example.cpp.s
.PHONY : example/client/video_client_example.cpp.s

example/client/vui_client_example.o: example/client/vui_client_example.cpp.o

.PHONY : example/client/vui_client_example.o

# target to build an object file
example/client/vui_client_example.cpp.o:
	$(MAKE) -f CMakeFiles/vui_client_example.dir/build.make CMakeFiles/vui_client_example.dir/example/client/vui_client_example.cpp.o
.PHONY : example/client/vui_client_example.cpp.o

example/client/vui_client_example.i: example/client/vui_client_example.cpp.i

.PHONY : example/client/vui_client_example.i

# target to preprocess a source file
example/client/vui_client_example.cpp.i:
	$(MAKE) -f CMakeFiles/vui_client_example.dir/build.make CMakeFiles/vui_client_example.dir/example/client/vui_client_example.cpp.i
.PHONY : example/client/vui_client_example.cpp.i

example/client/vui_client_example.s: example/client/vui_client_example.cpp.s

.PHONY : example/client/vui_client_example.s

# target to generate assembly for a file
example/client/vui_client_example.cpp.s:
	$(MAKE) -f CMakeFiles/vui_client_example.dir/build.make CMakeFiles/vui_client_example.dir/example/client/vui_client_example.cpp.s
.PHONY : example/client/vui_client_example.cpp.s

example/helloworld/HelloWorldData.o: example/helloworld/HelloWorldData.cpp.o

.PHONY : example/helloworld/HelloWorldData.o

# target to build an object file
example/helloworld/HelloWorldData.cpp.o:
	$(MAKE) -f CMakeFiles/test_publisher.dir/build.make CMakeFiles/test_publisher.dir/example/helloworld/HelloWorldData.cpp.o
	$(MAKE) -f CMakeFiles/test_subscriber.dir/build.make CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.o
.PHONY : example/helloworld/HelloWorldData.cpp.o

example/helloworld/HelloWorldData.i: example/helloworld/HelloWorldData.cpp.i

.PHONY : example/helloworld/HelloWorldData.i

# target to preprocess a source file
example/helloworld/HelloWorldData.cpp.i:
	$(MAKE) -f CMakeFiles/test_publisher.dir/build.make CMakeFiles/test_publisher.dir/example/helloworld/HelloWorldData.cpp.i
	$(MAKE) -f CMakeFiles/test_subscriber.dir/build.make CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.i
.PHONY : example/helloworld/HelloWorldData.cpp.i

example/helloworld/HelloWorldData.s: example/helloworld/HelloWorldData.cpp.s

.PHONY : example/helloworld/HelloWorldData.s

# target to generate assembly for a file
example/helloworld/HelloWorldData.cpp.s:
	$(MAKE) -f CMakeFiles/test_publisher.dir/build.make CMakeFiles/test_publisher.dir/example/helloworld/HelloWorldData.cpp.s
	$(MAKE) -f CMakeFiles/test_subscriber.dir/build.make CMakeFiles/test_subscriber.dir/example/helloworld/HelloWorldData.cpp.s
.PHONY : example/helloworld/HelloWorldData.cpp.s

example/helloworld/publisher.o: example/helloworld/publisher.cpp.o

.PHONY : example/helloworld/publisher.o

# target to build an object file
example/helloworld/publisher.cpp.o:
	$(MAKE) -f CMakeFiles/test_publisher.dir/build.make CMakeFiles/test_publisher.dir/example/helloworld/publisher.cpp.o
.PHONY : example/helloworld/publisher.cpp.o

example/helloworld/publisher.i: example/helloworld/publisher.cpp.i

.PHONY : example/helloworld/publisher.i

# target to preprocess a source file
example/helloworld/publisher.cpp.i:
	$(MAKE) -f CMakeFiles/test_publisher.dir/build.make CMakeFiles/test_publisher.dir/example/helloworld/publisher.cpp.i
.PHONY : example/helloworld/publisher.cpp.i

example/helloworld/publisher.s: example/helloworld/publisher.cpp.s

.PHONY : example/helloworld/publisher.s

# target to generate assembly for a file
example/helloworld/publisher.cpp.s:
	$(MAKE) -f CMakeFiles/test_publisher.dir/build.make CMakeFiles/test_publisher.dir/example/helloworld/publisher.cpp.s
.PHONY : example/helloworld/publisher.cpp.s

example/helloworld/subscriber.o: example/helloworld/subscriber.cpp.o

.PHONY : example/helloworld/subscriber.o

# target to build an object file
example/helloworld/subscriber.cpp.o:
	$(MAKE) -f CMakeFiles/test_subscriber.dir/build.make CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.o
.PHONY : example/helloworld/subscriber.cpp.o

example/helloworld/subscriber.i: example/helloworld/subscriber.cpp.i

.PHONY : example/helloworld/subscriber.i

# target to preprocess a source file
example/helloworld/subscriber.cpp.i:
	$(MAKE) -f CMakeFiles/test_subscriber.dir/build.make CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.i
.PHONY : example/helloworld/subscriber.cpp.i

example/helloworld/subscriber.s: example/helloworld/subscriber.cpp.s

.PHONY : example/helloworld/subscriber.s

# target to generate assembly for a file
example/helloworld/subscriber.cpp.s:
	$(MAKE) -f CMakeFiles/test_subscriber.dir/build.make CMakeFiles/test_subscriber.dir/example/helloworld/subscriber.cpp.s
.PHONY : example/helloworld/subscriber.cpp.s

example/high_level/follow_sin.o: example/high_level/follow_sin.cpp.o

.PHONY : example/high_level/follow_sin.o

# target to build an object file
example/high_level/follow_sin.cpp.o:
	$(MAKE) -f CMakeFiles/high_follow_sin.dir/build.make CMakeFiles/high_follow_sin.dir/example/high_level/follow_sin.cpp.o
.PHONY : example/high_level/follow_sin.cpp.o

example/high_level/follow_sin.i: example/high_level/follow_sin.cpp.i

.PHONY : example/high_level/follow_sin.i

# target to preprocess a source file
example/high_level/follow_sin.cpp.i:
	$(MAKE) -f CMakeFiles/high_follow_sin.dir/build.make CMakeFiles/high_follow_sin.dir/example/high_level/follow_sin.cpp.i
.PHONY : example/high_level/follow_sin.cpp.i

example/high_level/follow_sin.s: example/high_level/follow_sin.cpp.s

.PHONY : example/high_level/follow_sin.s

# target to generate assembly for a file
example/high_level/follow_sin.cpp.s:
	$(MAKE) -f CMakeFiles/high_follow_sin.dir/build.make CMakeFiles/high_follow_sin.dir/example/high_level/follow_sin.cpp.s
.PHONY : example/high_level/follow_sin.cpp.s

example/high_level/sportmode_test.o: example/high_level/sportmode_test.cpp.o

.PHONY : example/high_level/sportmode_test.o

# target to build an object file
example/high_level/sportmode_test.cpp.o:
	$(MAKE) -f CMakeFiles/sportmode_test.dir/build.make CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.o
.PHONY : example/high_level/sportmode_test.cpp.o

example/high_level/sportmode_test.i: example/high_level/sportmode_test.cpp.i

.PHONY : example/high_level/sportmode_test.i

# target to preprocess a source file
example/high_level/sportmode_test.cpp.i:
	$(MAKE) -f CMakeFiles/sportmode_test.dir/build.make CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.i
.PHONY : example/high_level/sportmode_test.cpp.i

example/high_level/sportmode_test.s: example/high_level/sportmode_test.cpp.s

.PHONY : example/high_level/sportmode_test.s

# target to generate assembly for a file
example/high_level/sportmode_test.cpp.s:
	$(MAKE) -f CMakeFiles/sportmode_test.dir/build.make CMakeFiles/sportmode_test.dir/example/high_level/sportmode_test.cpp.s
.PHONY : example/high_level/sportmode_test.cpp.s

example/humanoid/high_level/h1_arm_sdk_dds_example.o: example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.o

.PHONY : example/humanoid/high_level/h1_arm_sdk_dds_example.o

# target to build an object file
example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.o:
	$(MAKE) -f CMakeFiles/h1_arm_sdk_dds_example.dir/build.make CMakeFiles/h1_arm_sdk_dds_example.dir/example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.o
.PHONY : example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.o

example/humanoid/high_level/h1_arm_sdk_dds_example.i: example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.i

.PHONY : example/humanoid/high_level/h1_arm_sdk_dds_example.i

# target to preprocess a source file
example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.i:
	$(MAKE) -f CMakeFiles/h1_arm_sdk_dds_example.dir/build.make CMakeFiles/h1_arm_sdk_dds_example.dir/example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.i
.PHONY : example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.i

example/humanoid/high_level/h1_arm_sdk_dds_example.s: example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.s

.PHONY : example/humanoid/high_level/h1_arm_sdk_dds_example.s

# target to generate assembly for a file
example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.s:
	$(MAKE) -f CMakeFiles/h1_arm_sdk_dds_example.dir/build.make CMakeFiles/h1_arm_sdk_dds_example.dir/example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.s
.PHONY : example/humanoid/high_level/h1_arm_sdk_dds_example.cpp.s

example/humanoid/high_level/h1_loco_example_client.o: example/humanoid/high_level/h1_loco_example_client.cpp.o

.PHONY : example/humanoid/high_level/h1_loco_example_client.o

# target to build an object file
example/humanoid/high_level/h1_loco_example_client.cpp.o:
	$(MAKE) -f CMakeFiles/h1_loco_example_client.dir/build.make CMakeFiles/h1_loco_example_client.dir/example/humanoid/high_level/h1_loco_example_client.cpp.o
.PHONY : example/humanoid/high_level/h1_loco_example_client.cpp.o

example/humanoid/high_level/h1_loco_example_client.i: example/humanoid/high_level/h1_loco_example_client.cpp.i

.PHONY : example/humanoid/high_level/h1_loco_example_client.i

# target to preprocess a source file
example/humanoid/high_level/h1_loco_example_client.cpp.i:
	$(MAKE) -f CMakeFiles/h1_loco_example_client.dir/build.make CMakeFiles/h1_loco_example_client.dir/example/humanoid/high_level/h1_loco_example_client.cpp.i
.PHONY : example/humanoid/high_level/h1_loco_example_client.cpp.i

example/humanoid/high_level/h1_loco_example_client.s: example/humanoid/high_level/h1_loco_example_client.cpp.s

.PHONY : example/humanoid/high_level/h1_loco_example_client.s

# target to generate assembly for a file
example/humanoid/high_level/h1_loco_example_client.cpp.s:
	$(MAKE) -f CMakeFiles/h1_loco_example_client.dir/build.make CMakeFiles/h1_loco_example_client.dir/example/humanoid/high_level/h1_loco_example_client.cpp.s
.PHONY : example/humanoid/high_level/h1_loco_example_client.cpp.s

example/humanoid/low_level/humanoid.o: example/humanoid/low_level/humanoid.cpp.o

.PHONY : example/humanoid/low_level/humanoid.o

# target to build an object file
example/humanoid/low_level/humanoid.cpp.o:
	$(MAKE) -f CMakeFiles/h1_low_level_example.dir/build.make CMakeFiles/h1_low_level_example.dir/example/humanoid/low_level/humanoid.cpp.o
.PHONY : example/humanoid/low_level/humanoid.cpp.o

example/humanoid/low_level/humanoid.i: example/humanoid/low_level/humanoid.cpp.i

.PHONY : example/humanoid/low_level/humanoid.i

# target to preprocess a source file
example/humanoid/low_level/humanoid.cpp.i:
	$(MAKE) -f CMakeFiles/h1_low_level_example.dir/build.make CMakeFiles/h1_low_level_example.dir/example/humanoid/low_level/humanoid.cpp.i
.PHONY : example/humanoid/low_level/humanoid.cpp.i

example/humanoid/low_level/humanoid.s: example/humanoid/low_level/humanoid.cpp.s

.PHONY : example/humanoid/low_level/humanoid.s

# target to generate assembly for a file
example/humanoid/low_level/humanoid.cpp.s:
	$(MAKE) -f CMakeFiles/h1_low_level_example.dir/build.make CMakeFiles/h1_low_level_example.dir/example/humanoid/low_level/humanoid.cpp.s
.PHONY : example/humanoid/low_level/humanoid.cpp.s

example/jsonize/test_jsonize.o: example/jsonize/test_jsonize.cpp.o

.PHONY : example/jsonize/test_jsonize.o

# target to build an object file
example/jsonize/test_jsonize.cpp.o:
	$(MAKE) -f CMakeFiles/test_jsonize.dir/build.make CMakeFiles/test_jsonize.dir/example/jsonize/test_jsonize.cpp.o
.PHONY : example/jsonize/test_jsonize.cpp.o

example/jsonize/test_jsonize.i: example/jsonize/test_jsonize.cpp.i

.PHONY : example/jsonize/test_jsonize.i

# target to preprocess a source file
example/jsonize/test_jsonize.cpp.i:
	$(MAKE) -f CMakeFiles/test_jsonize.dir/build.make CMakeFiles/test_jsonize.dir/example/jsonize/test_jsonize.cpp.i
.PHONY : example/jsonize/test_jsonize.cpp.i

example/jsonize/test_jsonize.s: example/jsonize/test_jsonize.cpp.s

.PHONY : example/jsonize/test_jsonize.s

# target to generate assembly for a file
example/jsonize/test_jsonize.cpp.s:
	$(MAKE) -f CMakeFiles/test_jsonize.dir/build.make CMakeFiles/test_jsonize.dir/example/jsonize/test_jsonize.cpp.s
.PHONY : example/jsonize/test_jsonize.cpp.s

example/low_level/low_level.o: example/low_level/low_level.cpp.o

.PHONY : example/low_level/low_level.o

# target to build an object file
example/low_level/low_level.cpp.o:
	$(MAKE) -f CMakeFiles/low_level.dir/build.make CMakeFiles/low_level.dir/example/low_level/low_level.cpp.o
.PHONY : example/low_level/low_level.cpp.o

example/low_level/low_level.i: example/low_level/low_level.cpp.i

.PHONY : example/low_level/low_level.i

# target to preprocess a source file
example/low_level/low_level.cpp.i:
	$(MAKE) -f CMakeFiles/low_level.dir/build.make CMakeFiles/low_level.dir/example/low_level/low_level.cpp.i
.PHONY : example/low_level/low_level.cpp.i

example/low_level/low_level.s: example/low_level/low_level.cpp.s

.PHONY : example/low_level/low_level.s

# target to generate assembly for a file
example/low_level/low_level.cpp.s:
	$(MAKE) -f CMakeFiles/low_level.dir/build.make CMakeFiles/low_level.dir/example/low_level/low_level.cpp.s
.PHONY : example/low_level/low_level.cpp.s

example/low_level/stand_example_b2.o: example/low_level/stand_example_b2.cpp.o

.PHONY : example/low_level/stand_example_b2.o

# target to build an object file
example/low_level/stand_example_b2.cpp.o:
	$(MAKE) -f CMakeFiles/stand_example_b2.dir/build.make CMakeFiles/stand_example_b2.dir/example/low_level/stand_example_b2.cpp.o
.PHONY : example/low_level/stand_example_b2.cpp.o

example/low_level/stand_example_b2.i: example/low_level/stand_example_b2.cpp.i

.PHONY : example/low_level/stand_example_b2.i

# target to preprocess a source file
example/low_level/stand_example_b2.cpp.i:
	$(MAKE) -f CMakeFiles/stand_example_b2.dir/build.make CMakeFiles/stand_example_b2.dir/example/low_level/stand_example_b2.cpp.i
.PHONY : example/low_level/stand_example_b2.cpp.i

example/low_level/stand_example_b2.s: example/low_level/stand_example_b2.cpp.s

.PHONY : example/low_level/stand_example_b2.s

# target to generate assembly for a file
example/low_level/stand_example_b2.cpp.s:
	$(MAKE) -f CMakeFiles/stand_example_b2.dir/build.make CMakeFiles/stand_example_b2.dir/example/low_level/stand_example_b2.cpp.s
.PHONY : example/low_level/stand_example_b2.cpp.s

example/low_level/stand_example_go2.o: example/low_level/stand_example_go2.cpp.o

.PHONY : example/low_level/stand_example_go2.o

# target to build an object file
example/low_level/stand_example_go2.cpp.o:
	$(MAKE) -f CMakeFiles/stand_example_go2.dir/build.make CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.o
.PHONY : example/low_level/stand_example_go2.cpp.o

example/low_level/stand_example_go2.i: example/low_level/stand_example_go2.cpp.i

.PHONY : example/low_level/stand_example_go2.i

# target to preprocess a source file
example/low_level/stand_example_go2.cpp.i:
	$(MAKE) -f CMakeFiles/stand_example_go2.dir/build.make CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.i
.PHONY : example/low_level/stand_example_go2.cpp.i

example/low_level/stand_example_go2.s: example/low_level/stand_example_go2.cpp.s

.PHONY : example/low_level/stand_example_go2.s

# target to generate assembly for a file
example/low_level/stand_example_go2.cpp.s:
	$(MAKE) -f CMakeFiles/stand_example_go2.dir/build.make CMakeFiles/stand_example_go2.dir/example/low_level/stand_example_go2.cpp.s
.PHONY : example/low_level/stand_example_go2.cpp.s

example/state_machine/main.o: example/state_machine/main.cpp.o

.PHONY : example/state_machine/main.o

# target to build an object file
example/state_machine/main.cpp.o:
	$(MAKE) -f CMakeFiles/state_machine_example.dir/build.make CMakeFiles/state_machine_example.dir/example/state_machine/main.cpp.o
.PHONY : example/state_machine/main.cpp.o

example/state_machine/main.i: example/state_machine/main.cpp.i

.PHONY : example/state_machine/main.i

# target to preprocess a source file
example/state_machine/main.cpp.i:
	$(MAKE) -f CMakeFiles/state_machine_example.dir/build.make CMakeFiles/state_machine_example.dir/example/state_machine/main.cpp.i
.PHONY : example/state_machine/main.cpp.i

example/state_machine/main.s: example/state_machine/main.cpp.s

.PHONY : example/state_machine/main.s

# target to generate assembly for a file
example/state_machine/main.cpp.s:
	$(MAKE) -f CMakeFiles/state_machine_example.dir/build.make CMakeFiles/state_machine_example.dir/example/state_machine/main.cpp.s
.PHONY : example/state_machine/main.cpp.s

example/wireless/wireless.o: example/wireless/wireless.cpp.o

.PHONY : example/wireless/wireless.o

# target to build an object file
example/wireless/wireless.cpp.o:
	$(MAKE) -f CMakeFiles/wireless.dir/build.make CMakeFiles/wireless.dir/example/wireless/wireless.cpp.o
.PHONY : example/wireless/wireless.cpp.o

example/wireless/wireless.i: example/wireless/wireless.cpp.i

.PHONY : example/wireless/wireless.i

# target to preprocess a source file
example/wireless/wireless.cpp.i:
	$(MAKE) -f CMakeFiles/wireless.dir/build.make CMakeFiles/wireless.dir/example/wireless/wireless.cpp.i
.PHONY : example/wireless/wireless.cpp.i

example/wireless/wireless.s: example/wireless/wireless.cpp.s

.PHONY : example/wireless/wireless.s

# target to generate assembly for a file
example/wireless/wireless.cpp.s:
	$(MAKE) -f CMakeFiles/wireless.dir/build.make CMakeFiles/wireless.dir/example/wireless/wireless.cpp.s
.PHONY : example/wireless/wireless.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... h1_arm_sdk_dds_example"
	@echo "... h1_low_level_example"
	@echo "... test_publisher"
	@echo "... video_client_example"
	@echo "... test_subscriber"
	@echo "... h1_loco_example_client"
	@echo "... test_jsonize"
	@echo "... vui_client_example"
	@echo "... stand_example_b2"
	@echo "... sport_client_example"
	@echo "... low_level"
	@echo "... stand_example_go2"
	@echo "... wireless"
	@echo "... high_follow_sin"
	@echo "... advanced_gamepad"
	@echo "... state_machine_example"
	@echo "... sportmode_test"
	@echo "... robot_state_client_example"
	@echo "... example/advanced_gamepad/main.o"
	@echo "... example/advanced_gamepad/main.i"
	@echo "... example/advanced_gamepad/main.s"
	@echo "... example/client/robot_state_client_example.o"
	@echo "... example/client/robot_state_client_example.i"
	@echo "... example/client/robot_state_client_example.s"
	@echo "... example/client/sport_client_example.o"
	@echo "... example/client/sport_client_example.i"
	@echo "... example/client/sport_client_example.s"
	@echo "... example/client/video_client_example.o"
	@echo "... example/client/video_client_example.i"
	@echo "... example/client/video_client_example.s"
	@echo "... example/client/vui_client_example.o"
	@echo "... example/client/vui_client_example.i"
	@echo "... example/client/vui_client_example.s"
	@echo "... example/helloworld/HelloWorldData.o"
	@echo "... example/helloworld/HelloWorldData.i"
	@echo "... example/helloworld/HelloWorldData.s"
	@echo "... example/helloworld/publisher.o"
	@echo "... example/helloworld/publisher.i"
	@echo "... example/helloworld/publisher.s"
	@echo "... example/helloworld/subscriber.o"
	@echo "... example/helloworld/subscriber.i"
	@echo "... example/helloworld/subscriber.s"
	@echo "... example/high_level/follow_sin.o"
	@echo "... example/high_level/follow_sin.i"
	@echo "... example/high_level/follow_sin.s"
	@echo "... example/high_level/sportmode_test.o"
	@echo "... example/high_level/sportmode_test.i"
	@echo "... example/high_level/sportmode_test.s"
	@echo "... example/humanoid/high_level/h1_arm_sdk_dds_example.o"
	@echo "... example/humanoid/high_level/h1_arm_sdk_dds_example.i"
	@echo "... example/humanoid/high_level/h1_arm_sdk_dds_example.s"
	@echo "... example/humanoid/high_level/h1_loco_example_client.o"
	@echo "... example/humanoid/high_level/h1_loco_example_client.i"
	@echo "... example/humanoid/high_level/h1_loco_example_client.s"
	@echo "... example/humanoid/low_level/humanoid.o"
	@echo "... example/humanoid/low_level/humanoid.i"
	@echo "... example/humanoid/low_level/humanoid.s"
	@echo "... example/jsonize/test_jsonize.o"
	@echo "... example/jsonize/test_jsonize.i"
	@echo "... example/jsonize/test_jsonize.s"
	@echo "... example/low_level/low_level.o"
	@echo "... example/low_level/low_level.i"
	@echo "... example/low_level/low_level.s"
	@echo "... example/low_level/stand_example_b2.o"
	@echo "... example/low_level/stand_example_b2.i"
	@echo "... example/low_level/stand_example_b2.s"
	@echo "... example/low_level/stand_example_go2.o"
	@echo "... example/low_level/stand_example_go2.i"
	@echo "... example/low_level/stand_example_go2.s"
	@echo "... example/state_machine/main.o"
	@echo "... example/state_machine/main.i"
	@echo "... example/state_machine/main.s"
	@echo "... example/wireless/wireless.o"
	@echo "... example/wireless/wireless.i"
	@echo "... example/wireless/wireless.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

