# 指定了用于G1机器人的预训练策略（神经网络模型）文件的路径
policy_path: "{LEGGED_GYM_ROOT_DIR}/deploy/pre_train/g1/policy_lstm_1.pt"

xml_path: "{LEGGED_GYM_ROOT_DIR}/resources/robots/g1_description/scene.xml"

# Total simulation time
simulation_duration: 60.0
# Simulation time step
simulation_dt: 0.002
# Controller update frequency (meets the requirement of simulation_dt * controll_decimation=0.02; 50Hz)
# 降采样因子：神经网络策略（控制器）的更新频率
control_decimation: 10

# PD控制器的每个关节的比例增益
kps: [100, 100, 100, 150, 40, 40, 100, 100, 100, 150, 40, 40]

# PD控制器的每个关节的微分增益（增加系统阻尼，减小震荡）
kds: [2, 2, 2, 4, 2, 2, 2, 2, 2, 4, 2, 2]

# 机器人12个关节的默认初始角度（弧度）
default_angles: [-0.1,  0.0,  0.0,  0.3, -0.2, 0.0, 
                  -0.1,  0.0,  0.0,  0.3, -0.2, 0.0]

ang_vel_scale: 0.25
dof_pos_scale: 1.0
dof_vel_scale: 0.05
action_scale: 0.25
cmd_scale: [2.0, 2.0, 0.25]
# 动作空间的维度
num_actions: 12
# 观测空间的维度
num_obs: 47

cmd_init: [0.5, 0, 0]