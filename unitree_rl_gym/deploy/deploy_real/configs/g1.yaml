# 
control_dt: 0.02

msg_type: "hg"     # "hg" or "go"
imu_type: "pelvis"    # "torso" or "pelvis"

lowcmd_topic: "rt/lowcmd"
lowstate_topic: "rt/lowstate"

policy_path: "{LEGGED_GYM_ROOT_DIR}/deploy/pre_train/g1/motion.pt"

leg_joint2motor_idx: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
kps: [100, 100, 100, 150, 40, 40, 100, 100, 100, 150, 40, 40]
kds: [2, 2, 2, 4, 2, 2, 2, 2, 2, 4, 2, 2]
default_angles: [-0.1,  0.0,  0.0,  0.3, -0.2, 0.0, 
                  -0.1,  0.0,  0.0,  0.3, -0.2, 0.0]

arm_waist_joint2motor_idx: [12, 13, 14, 
                            15, 16, 17, 18, 19, 20, 21, 
                            22, 23, 24, 25, 26, 27, 28]

arm_waist_kps: [300, 300, 300,
                100, 100, 50, 50, 20, 20, 20,
                100, 100, 50, 50, 20, 20, 20]

arm_waist_kds: [3, 3, 3, 
                2, 2, 2, 2, 1, 1, 1,
                2, 2, 2, 2, 1, 1, 1]

arm_waist_target: [ 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 0]

ang_vel_scale: 0.25
dof_pos_scale: 1.0
dof_vel_scale: 0.05
action_scale: 0.25
cmd_scale: [2.0, 2.0, 0.25]
num_actions: 12
num_obs: 47

max_cmd: [0.8, 0.5, 1.57]
