# 
control_dt: 0.02

msg_type: "hg"     # "hg" or "go"
imu_type: "torso"    # "torso" or "pelvis"

lowcmd_topic: "rt/lowcmd"
lowstate_topic: "rt/lowstate"

policy_path: "{LEGGED_GYM_ROOT_DIR}/deploy/pre_train/h1_2/motion.pt"

leg_joint2motor_idx: [0, 1, 2, 3, 4, 5, 
                      6, 7, 8, 9, 10, 11]
kps: [200, 200, 200, 300, 40, 40, 
      200, 200, 200, 300, 40, 40]
kds: [2.5, 2.5, 2.5, 4, 2, 2,  
      2.5, 2.5, 2.5, 4, 2, 2]
default_angles: [0, -0.16, 0.0, 0.36, -0.2, 0.0, 
                 0, -0.16, 0.0, 0.36, -0.2, 0.0]

arm_waist_joint2motor_idx: [12, 
                            13, 14, 15, 16, 17, 18, 19,
                            20, 21, 22, 23, 24, 25, 26]

arm_waist_kps: [300,
                120, 120, 120, 80, 80, 80, 80,
                120, 120, 120, 80, 80, 80, 80]

arm_waist_kds: [3,
                2, 2, 2, 1, 1, 1, 1,
                2, 2, 2, 1, 1, 1, 1]

arm_waist_target: [ 0,
                    0, 0, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 0]

ang_vel_scale: 0.25
dof_pos_scale: 1.0
dof_vel_scale: 0.05
action_scale: 0.25
cmd_scale: [2.0, 2.0, 0.25]
num_actions: 12
num_obs: 47

max_cmd: [0.8, 0.5, 1.57]
