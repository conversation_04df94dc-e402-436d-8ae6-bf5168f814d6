# 
control_dt: 0.02

msg_type: "go"     # "hg" or "go"
imu_type: "torso"    # "torso" or "pelvis"

weak_motor: [10, 11, 12, 13, 14, 15, 16, 17, 18, 19]

lowcmd_topic: "rt/lowcmd"
lowstate_topic: "rt/lowstate"

policy_path: "{LEGGED_GYM_ROOT_DIR}/deploy/pre_train/h1/motion.pt"

leg_joint2motor_idx: [7, 3, 4, 5, 10, 8, 0, 1, 2, 11]
kps: [150, 150, 150, 200, 40,  150, 150, 150, 200, 40]
kds: [2, 2, 2, 4, 2,  2, 2, 2, 4, 2]
default_angles: [0,  0.0,  -0.1,  0.3, -0.2,
                 0,  0.0,  -0.1,  0.3, -0.2]

arm_waist_joint2motor_idx: [6,
                            16, 17, 18, 19,
                            12, 13, 14, 15]

arm_waist_kps: [300,
                100, 100, 50, 50,
                100, 100, 50, 50]

arm_waist_kds: [3, 
                2, 2, 2, 2,
                2, 2, 2, 2]

arm_waist_target: [ 0,
                    0, 0, 0, 0,
                    0, 0, 0, 0]

ang_vel_scale: 0.25
dof_pos_scale: 1.0
dof_vel_scale: 0.05
action_scale: 0.25
cmd_scale: [2.0, 2.0, 0.25]
num_actions: 10
num_obs: 41

max_cmd: [0.8, 0.5, 1.57]
